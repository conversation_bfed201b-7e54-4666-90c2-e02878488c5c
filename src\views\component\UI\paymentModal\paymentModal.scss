@import "../../../../assets/scss/variable.scss";

.payment-modal {
  .MuiDialog-paper {
    max-width: 570px;
    width: 100%;
    margin: 12px;
    border-radius: 8px;

    .modal-title {
      background-color: #e8eaec;
      padding: 21px 18px 9px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 31.36px;
        line-height: 31.36px;
        font-weight: 400;
        font-family: $primaryFont;
        color: $color-Black;
      }

      .MuiButtonBase-root {
        padding: 0px;

        svg {
          path {
            fill: #989898;
          }
        }
      }

      .close-icon {
        position: absolute;
        top: 18px;
        right: 18px;

        @media (max-width: 799px) {
          top: 12px;
          right: 12px;
        }
      }
    }

    .payment-details {
      padding: 18px 18px 21px;

      @media (max-width: 799px) {
        padding: 18px 12px;
      }

      .payment-select {
        .payment-radioGroup {
          // flex-direction: row;
          // justify-content: space-between;

          .payment-card-flex {
            display: flex;
            align-items: baseline;
            justify-content: space-between;
          }

          .MuiFormControlLabel-root {
            &:not(:last-child) {
              margin-bottom: 19px;
            }
          }

          .MuiButtonBase-root {
            padding: 0px 9px 0px 12px;
          }

          .payment-label {
            display: flex;
            align-items: center;

            .label {
              font-size: 16px;
              line-height: 19px;
              font-weight: 600;
              font-family: $regulerFont;
              color: $color-Black;
              margin-right: 8px;
            }

            .card-section {
              display: flex;
              column-gap: 8px;
            }
          }

          .save-card {
            align-items: flex-start;

            .MuiTypography-root {
              width: 100%;
            }

            .save-payment-card-label {
              display: flex;
              justify-content: space-between;

              p {
                font-size: 16px;
                line-height: 19px;
                font-weight: 400;
                font-family: $regulerFont;
                color: $color-Black;
              }

              .bold {
                font-weight: 600;
              }

              .disabled {
                opacity: 0.8;
                cursor: default;
              }
            }
          }
        }
      }

      .card-details {
        margin-top: 28px;
      }

      .mb-19 {
        margin-bottom: 19px;
      }

      .date-cvv-wrap {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        column-gap: 18px;

        @media (max-width: 799px) {
          flex-direction: column;
          row-gap: 18px;

          .card-info {
            width: 100%;
          }
        }
      }

      .card-info {
        .textfield-text {
          font-size: 16px;
          line-height: 19px;
          font-weight: 600;
          font-family: $regulerFont;
          margin-bottom: 4px;
          color: $color-Black;

          @media (max-width: 799px) {
            font-size: 11.42px;
            line-height: 14px;
          }

          .star {
            color: #d84727;
          }
        }

        .cvv-text {
          display: flex;
          align-items: center;
          column-gap: 5px;
        }

        .details-textfield {
          width: 100%;

          .MuiInputBase-root {
            font-size: 16px;
            line-height: 19px;
            font-weight: 400;
            font-family: $regulerFont;
            color: $color-Black;
            border-radius: 8px;

            .MuiInputBase-input {
              padding: 13px 14px;
            }

            .MuiOutlinedInput-notchedOutline {
              border-color: #d4d6d8;
            }

            input[type="number"]::-webkit-inner-spin-button,
            input[type="number"]::-webkit-outer-spin-button {
              display: none;
            }
          }

          .Mui-focused {
            .MuiOutlinedInput-notchedOutline {
              border-color: $color-Accent-1;
              border-width: 1px;
            }
          }
        }
      }

      .select-box-wrap {
        width: 50%;

        @media (max-width: 659px) {
          max-width: none;
        }

        .select__control {
          box-shadow: none;
          // border: 1px solid red;
          padding: 0px;
          margin: 0px;
        }

        .select__control,
        .react-select__control {
          // &.select__control--is-disabled {
          //   // border-color: $border-color;
          //   // .select__indicator-separator {
          //   //   //   background-color: $border-color;
          //   // }
          // }

          &.select__control--is-focused,
          &.react-select__control--is-focused {
            border-color: $color-Accent-1;
          }

          &.select__control--menu-is-open {
            box-shadow: none;
          }

          .select__indicator svg {
            cursor: pointer;
          }

          .select__indicator-separator {
            display: none;
          }

          .select__single-value {
            border: none;
            font-size: 16px;
            line-height: 20px;
            padding-left: 10px;
            font-family: $regulerFont;
            outline: none;
            cursor: pointer;
            margin: 0px;
          }

          .select__value-container {
            border: none;
            padding: 0px;
            margin: 0px;

            .select__input-container {
              padding-left: 10px;
              font-size: 16px;
              line-height: 20px;
              font-family: $regulerFont;
            }
          }

          .select__placeholder {
            font-size: 16px;
            line-height: 20px;
            padding-left: 10px;
            cursor: pointer;
            font-family: $regulerFont;
          }
        }

        .select__menu {
          margin: 0px;
          border-radius: 0px;
          padding: 0px;
          z-index: 999;
        }

        .select__menu-list {
          padding: 0px;
        }

        // Select Menu
        .select__menu,
        .react-select__menu {
          .select__menu-list,
          .react-select__menu-list {
            .select__option,
            .react-select__option {
              cursor: pointer;
              font-size: 16px;
              color: $color-Black;
              font-family: $regulerFont;
              line-height: 19px;
              padding: 11px;

              &.select__option--is-focused {
                background-color: #d4d6d8;
                color: #000;
              }

              &.select__option--is-selected {
                background-color: #d4d6d8;
                color: $color-Black;
              }
            }
          }
        }

        .select {
          .select__control {
            min-height: 45px;
            border-radius: 8px;
            width: 100%;
          }
        }
      }

      .date-card-info {
        width: 50%;
      }

      .cvv-card-info {
        width: 50%;
      }

      .checkBox-wrap {
        // border-bottom: 1px solid #4455C7;
        padding-bottom: 18px;

        .documentsRead-check {
          .MuiButtonBase-root {
            margin-right: 12px;
            padding: 0px 0px 0px 10px;

            &:hover {
              background-color: transparent;
            }

            .MuiSvgIcon-root {
              path {
                fill: #d4d6d8;
              }
            }

            .MuiTouchRipple-root {
              display: none;
            }
          }

          .Mui-checked {
            .MuiSvgIcon-root {
              path {
                fill: $color-Primary;
              }
            }
          }

          .MuiTypography-root {
            font-size: 16px;
            line-height: 19px;
            font-weight: 400;
            font-family: $regulerFont;
            letter-spacing: 0px;
            color: #989898;
          }
        }
      }

      .total-amount-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 7px;

        p {
          font-size: 16px;
          line-height: 19px;
          font-weight: 400;
          font-family: $regulerFont;
          color: $color-Black;
        }

        .bold {
          font-weight: 600;
        }
      }

      .border-bottom-card {
        padding-bottom: 10px;
        border-bottom: 1px solid $color-Accent-1;
      }

      .total-amount {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;
        border-bottom: 1px solid #d4d6d8;
        margin-top: 7px;

        p {
          font-size: 16px;
          line-height: 19px;
          font-weight: 400;
          font-family: $regulerFont;
          color: $color-Black;
        }

        .bold {
          font-weight: 600;
        }
      }

      .subscribe-wrap-btn {
        text-align: center;
        margin-top: 19px;
        margin-bottom: 8px;

        .subscribe-btn {
          padding: 13px 24px 12px;
          background-color: $color-Accent-1;
          color: $color-White;
          font-size: 16px;
          line-height: 19px;
          font-weight: 400;
          font-family: $regulerFont;
          border-radius: 8px;
          text-transform: capitalize;
          box-shadow: none;

          .MuiTouchRipple-root {
            display: none;
          }
        }
      }

      .subscribe-note {
        text-align: center;

        p {
          font-size: 16px;
          line-height: 19px;
          font-weight: 400;
          font-family: $regulerFont;
          color: #989898;

          @media (max-width: 799px) {
            font-size: 11.42px;
            line-height: 14px;
          }
        }
      }
    }
  }
}
