# SmartB WebApp

**SmartB** is Australia's smartest online sports news and odds comparison platform. This React-based web application provides comprehensive sports coverage, racing information, fantasy sports, tipping competitions, and odds comparison tools to help users make smarter betting decisions.

## 🏆 About SmartB

SmartB is Australia's best resource for the latest sports news, updates, fixtures, form guides and the tools you need to back more winners in any game. The platform offers:

- **Racing Coverage**: Horse racing, greyhound racing, and harness racing
- **Sports Coverage**: AFL, NRL, Cricket, Basketball, American Football, and more
- **Fantasy Sports**: SmartPlay daily fantasy sports platform
- **Tipping Competitions**: Create and join tipping competitions
- **Odds Comparison**: Smart Odds Comparison tool for better betting decisions
- **News & Analysis**: Latest sports news, expert tips, and analysis
- **Statistics**: Comprehensive track profiles and sports statistics

## 🚀 Quick Start

### Prerequisites

- Node.js 14.x or higher
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smartb-webapp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory with the following variables:
   ```env
   REACT_APP_API_BASE_URL=https://staging.smartb.au.sydney.digiground.com.au/api/
   REACT_APP_FANTASY_API_URL=https://staging.smartb.au.sydney.digiground.com.au/fantasy-api/fantasy
   REACT_APP_MEDIA_URL=https://media.staging.smartb.au.sydney.digiground.com.au/
   REACT_APP_COUNTRY_MEDIA_URL=https://media.staging.smartb.au.sydney.digiground.com.au/
   REACT_APP_WP_BASE_URL=https://staging.smartb.au.sydney.digiground.com.au/
   REACT_APP_FANTASY_API_BASE_URL=https://staging.smartb.au.sydney.digiground.com.au/fantasy
   REACT_APP_VERSION=v.0.1s
   REACT_APP_RELEASE=AU
   REACT_APP_API_SOCKET_BASE_URL=https://staging.smartb.au.sydney.digiground.com.au
   REACT_APP_SOCKET_PATH=/race-alert/socket.io/
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

   The application will open at `http://localhost:3000`

## 📁 Project Structure

```
smartb-webapp/
├── public/                     # Static files
│   ├── index.html             # Main HTML template
│   ├── manifest.json          # PWA manifest
│   └── favicon.ico            # App icon
├── src/
│   ├── assets/                # Static assets
│   │   ├── images/           # Image files
│   │   ├── scss/             # SCSS stylesheets
│   │   └── fonts/            # Font files
│   ├── components/           # Reusable UI components
│   │   ├── Common/           # Common components
│   │   ├── Loader.js         # Loading components
│   │   └── Page.js           # Page wrapper component
│   ├── helpers/              # Utility functions and configurations
│   │   ├── Axios/            # API configuration
│   │   ├── constants/        # App constants
│   │   ├── context/          # React context providers
│   │   ├── store/            # Redux store configuration
│   │   └── utils/            # Utility functions
│   ├── layouts/              # Layout components
│   │   ├── MainLayout/       # Main application layout
│   │   ├── AuthLayout/       # Authentication layout
│   │   └── AdminLayout/      # Admin layout
│   ├── views/                # Page components
│   │   ├── component/        # Feature-specific components
│   │   └── pages/            # Page-level components
│   ├── theme/                # Material-UI theme configuration
│   ├── routes.js             # Application routing
│   ├── App.js                # Main application component
│   └── index.js              # Application entry point
├── Dockerfile                # Docker configuration
├── Jenkinsfile              # CI/CD pipeline configuration
├── package.json             # Dependencies and scripts
└── README.md               # Project documentation
```

## 🛠️ Available Scripts

In the project directory, you can run:

### `npm start`
Runs the app in development mode. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### `npm run build`
Builds the app for production to the `build` folder. It correctly bundles React in production mode and optimizes the build for the best performance.

### `npm test`
Launches the test runner in the interactive watch mode.

### `npm run eject`
**Note: this is a one-way operation. Once you `eject`, you can't go back!**

## 🏗️ Key Technologies

### Frontend Framework
- **React 18.3.1** - Modern React with hooks and functional components
- **React Router 6.26.1** - Client-side routing
- **Material-UI 6.0.1** - React component library
- **Styled Components 6.1.13** - CSS-in-JS styling

### State Management
- **Redux 5.0.1** - Predictable state container
- **React Redux 9.1.2** - React bindings for Redux
- **Zustand 4.5.5** - Lightweight state management

### Data Visualization
- **Recharts 2.12.7** - Chart library for React
- **Victory 37.1.0** - Data visualization components

### Form Handling
- **Formik 2.4.6** - Form library for React
- **Yup 1.4.0** - Schema validation

### UI/UX Libraries
- **React Beautiful DnD 13.1.1** - Drag and drop functionality
- **React Slick 0.30.2** - Carousel component
- **React Toastify 10.0.5** - Toast notifications
- **React Infinite Scroll 6.1.0** - Infinite scrolling
- **React Lottie 1.2.4** - Animation library

### Utilities
- **Axios 1.7.7** - HTTP client
- **Moment.js 2.30.1** - Date manipulation
- **Lodash 4.17.21** - Utility library
- **Socket.io Client 4.7.2** - Real-time communication

## 🌟 Main Features

### 🏇 Racing Section
- **Live Racing Data**: Real-time horse, greyhound, and harness racing information
- **Race Cards**: Detailed race information with form guides
- **Odds Comparison**: Compare odds across multiple bookmakers
- **Track Profiles**: Comprehensive track statistics and information
- **Racing News**: Latest racing news and updates

### ⚽ Sports Section
- **Multi-Sport Coverage**: AFL, NRL, Cricket, Basketball, American Football, Rugby Union
- **Live Scores**: Real-time match scores and updates
- **Team Statistics**: Comprehensive team and player statistics
- **Match Previews**: Expert analysis and match previews
- **Sports News**: Latest sports news and updates

### 🎮 SmartPlay (Fantasy Sports)
- **Daily Fantasy Sports**: Create fantasy teams for various sports
- **Contests**: Join public and private fantasy contests
- **Player Statistics**: Detailed player performance data
- **Leaderboards**: Track your performance against other players

### 🏆 Tipping Competitions
- **Create Competitions**: Set up custom tipping competitions
- **Join Competitions**: Participate in public and private competitions
- **Leaderboards**: Track competition standings
- **Prizes**: Win prizes in various competitions

### 📊 Smart Odds Comparison
- **Multi-Bookmaker Comparison**: Compare odds across multiple betting platforms
- **Best Odds Finder**: Automatically find the best available odds
- **Price Alerts**: Get notified when odds reach your target price
- **Historical Data**: Track odds movements over time

### 📰 SmartInfo (News & Analysis)
- **Latest News**: Up-to-date sports and racing news
- **Expert Analysis**: Professional tips and analysis
- **Bookmaker Reviews**: Comprehensive bookmaker information
- **Recommended Websites**: Curated list of useful betting resources

## 🔧 Configuration

### Environment Variables

The application uses environment variables for configuration. Key variables include:

| Variable | Description | Example |
|----------|-------------|---------|
| `REACT_APP_API_BASE_URL` | Main API endpoint | `https://smartb.com.au/api/` |
| `REACT_APP_FANTASY_API_BASE_URL` | Fantasy sports API endpoint | `https://smartb.com.au/fantasy` |
| `REACT_APP_MEDIA_URL` | Media files URL | `https://media.smartb.com.au/` |
| `REACT_APP_RELEASE` | Release version (AU/IN) | `AU` |
| `REACT_APP_API_SOCKET_BASE_URL` | WebSocket server URL | `https://smartb.com.au` |

### Multi-Region Support

The application supports multiple regions:
- **AU (Australia)**: Full feature set including racing and sports
- **IN (India)**: Customized feature set for Indian market

## 🐳 Docker Deployment

### Build Docker Image
```bash
docker build -t smartb-webapp .
```

### Run Docker Container
```bash
docker run -p 3000:80 smartb-webapp
```

### Docker Environment Variables
The Dockerfile accepts build arguments for environment configuration:
```bash
docker build \
  --build-arg REACT_APP_API_BASE_URL=https://smartb.com.au/api/ \
  --build-arg REACT_APP_FANTASY_API_BASE_URL=https://smartb.com.au/fantasy \
  -t smartb-webapp .
```

## 🚀 CI/CD Pipeline

The project uses Jenkins for continuous integration and deployment:

### Pipeline Stages
1. **Environment Setup**: Configure environment variables based on branch
2. **Build**: Create production build
3. **Test**: Run test suite
4. **Deploy**: Deploy to appropriate environment

### Branch-Based Deployment
- **staging**: Deploys to staging environment
- **testing**: Deploys to testing environment
- **master**: Deploys to production environment

## 📱 Mobile Applications

SmartB also offers mobile applications:
- **Android**: Available on Google Play Store
- **iOS**: Available on Apple App Store

## 🔒 Authentication & Security

- **JWT Token Authentication**: Secure user authentication
- **Role-Based Access Control**: Different access levels for users
- **Secure API Communication**: HTTPS-only API communication
- **Data Protection**: User data protection and privacy compliance

## 🧪 Testing

### Running Tests
```bash
npm test
```

### Test Coverage
The project includes:
- Unit tests for components
- Integration tests for API calls
- End-to-end testing for critical user flows

## 📈 Performance Optimization

- **Code Splitting**: Lazy loading of components
- **Image Optimization**: Optimized image loading
- **Caching**: Strategic caching of API responses
- **Bundle Optimization**: Minimized bundle sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style
- Follow ESLint configuration
- Use Prettier for code formatting
- Follow React best practices
- Write meaningful commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

## 👥 Team

**Developed by DigiGround**

## 📞 Support

For support and inquiries:
- Visit our website: [smartb.com.au](https://smartb.com.au)
- Follow us on social media for updates
- Contact our support team through the app

## 🔄 Version History

- **v1.0.0**: Initial release with core features
- **v0.1s**: Staging version with latest features
- **v0.1t**: Testing version for QA

---

**SmartB** - Making smarter bets, one click at a time! 🎯