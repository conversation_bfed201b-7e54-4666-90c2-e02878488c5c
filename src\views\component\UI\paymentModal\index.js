import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Button,
  Typography,
  RadioGroup,
  Radio,
  FormControlLabel,
  TextField,
  Checkbox,
} from "@mui/material";
import { ReactComponent as Checked } from "src/assets/images/billedChecked.svg";
import { ReactComponent as UnChecked } from "src/assets/images/billedUnChecked.svg";
import { ReactComponent as CheckBoxChecked } from "src/assets/images/checkBoxChecked.svg";
import { ReactComponent as CheckboxUnChecked } from "src/assets/images/checkboxUnChecked.svg";
import Cvvimage from "../../../../assets/images/cvvimage.png";
import { ToastContainer, toast } from "react-toastify";
import { Formik } from "formik";
import * as Yup from "yup";
import { IntlContext } from "src/App";
import { Config, fetchFromStorage } from "src/helpers/context";
import { fetchRestrictedRoute } from "src/helpers/store/Actions/RestrictedRoute";
import { useDispatch } from "react-redux";
import axiosInstance from "src/helpers/Axios/axiosInstance";
import Loader from "src/components/Loader";
import { fetchSubscriptionData } from "src/helpers/store/Actions/SubscriptionData";
import moment from "moment";
import "./paymentModal.scss";
import { useNavigate } from "react-router";
import { setApiMessage } from "src/helpers/commonFunctions";
import Select from "react-select";
import _ from "lodash";

const PaymentModal = ({
  paymentModalOpen,
  setPaymentModalOpen,
  handleClose,
  fetchGetPlanDetails,
  planId,
  planPrice,
  planDetails,
  compId,
  handleSupportedTeamModal,
  couponApply,
  couponCode,
  couponCodeDetails,
  handlePaymentStatus,
  selectedPlanDetails,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const [paymentMode, setPaymentMode] = useState("");
  const [cardsave, setCardSave] = useState(false);
  // const [CardSaveLoading, setCardSaveLoading] = useState(false);
  const [subscribeLoading, setSubscribeLoading] = useState(false);
  const [cardList, setCardList] = useState([]);
  const [cardLoader, setCardLoader] = useState(false);
  const [selectedCard, setSelectedCard] = useState("");
  const [cardFormValues, setCardFormValues] = useState(null);
  const [cardFormError, setCardFormError] = useState(null);
  const [cardValidate, setCardValidate] = useState(false);
  const [cardFeeInfo, setCardFeeInfo] = useState({});

  // Billing Address states
  const [country, setCountry] = useState([]);
  const [state, setState] = useState([]);
  const [searchCountry, setSearchCountry] = useState([]);
  const [searchState, setSearchState] = useState([]);
  const [isCountrySearch, setIsCountrySearch] = useState("");
  const [isStateSearch, setIsStateSearch] = useState("");
  const [countryId, setCountryId] = useState("");
  const [countryApiCount, setCountryApiCount] = useState(0);
  const [stateApiCount, setStateApiCount] = useState(0);
  const [pageCountry, setpageCountry] = useState(0);
  const [pageState, setpageState] = useState(0);
  const [searchCountryCount, setsearchCountryCount] = useState(0);
  const [searchStateCount, setsearchStateCount] = useState(0);
  const [isCountrySelectOpen, setisCountrySelectOpen] = useState(false);
  const [isStateSelectOpen, setisStateSelectOpen] = useState(false);
  const [saveAddressForFuture, setSaveAddressForFuture] = useState(false);

  useEffect(() => {
    if (compId) {
      const { cardname, cardNumber, expirationDate, cvv } =
        cardFormValues ?? {};
      let formattedDate = "";

      if (expirationDate) {
        const parts = expirationDate.split("/");
        if (parts?.length === 2) {
          const month = parts[0].padStart(2, "0");
          const year = parts[1];
          formattedDate = `${year}-${month}`;
        }
      }

      // Only validate if all fields are filled and no errors
      if (
        cardNumber?.length >= 16 &&
        expirationDate?.length === 7 &&
        cvv?.length === 3 &&
        !cardFormError?.cardname &&
        !cardFormError?.cardNumber &&
        !cardFormError?.expirationDate &&
        !cardFormError?.cvv
      ) {
        validateUserCard({
          cardname,
          cardNumber: cardNumber.replace(/\s/g, ""),
          cardExp: formattedDate,
          cvv,
          amount: planPrice ?? 0,
        });
      }
    }
  }, [cardFormValues, cardFormError]);

  const validateUserCard = async (cardData) => {
    setCardValidate(true);
    try {
      const payload = cardData?.cardId
        ? {
            cardId: cardData?.cardId,
            amount: cardData?.amount,
          }
        : {
            card: {
              card_holder_name: cardData?.cardname,
              card_exp: cardData?.cardExp,
              card_number: cardData?.cardNumber,
              cvv: cardData?.cvv,
            },
            amount: cardData?.amount,
          };

      const { status, data } = await axiosInstance.post(
        `subscription/card/validations`,
        payload,
      );

      if (status === 200) {
        setCardFeeInfo(data?.data);
        setCardValidate(false);
      } else {
        setCardValidate(false);
        setApiMessage("error", data?.message);
      }
    } catch (err) {
      setCardValidate(false);
      setApiMessage(
        "error",
        err?.response?.data?.message || "Card validation failed",
      );
    }
  };

  const googleTagData = (transaction_id) => {
    return {
      transaction_id: transaction_id,
      value: couponApply
        ? (
            Number(selectedPlanDetails?.amount ?? 0) -
            Number(couponCodeDetails?.discountAmountPrize ?? 0)
          )?.toFixed(2)
        : selectedPlanDetails?.amount
          ? Number(selectedPlanDetails?.amount)?.toFixed(2)
          : 0,
      currency: selectedPlanDetails?.currency,
      coupon: couponApply ? couponCodeDetails?.data?.code : "",
      items: [
        {
          item_id: selectedPlanDetails?.id,
          item_name: selectedPlanDetails?.name,
          coupon: couponApply ? couponCodeDetails?.data?.code : "",
          discount: couponApply ? couponCodeDetails?.discountAmountPrize : 0,
          item_brand: "SmartB",
          price: couponApply
            ? (
                Number(selectedPlanDetails?.amount ?? 0) -
                Number(couponCodeDetails?.discountAmountPrize ?? 0)
              )?.toFixed(2)
            : selectedPlanDetails?.amount
              ? Number(selectedPlanDetails?.amount)?.toFixed(2)
              : 0,
          quantity: 1,
        },
      ],
    };
  };

  useEffect(() => {
    fetchGetCardDetails();
    fetchCountry(0);
  }, [paymentModalOpen]);

  const fetchGetCardDetails = async () => {
    setCardLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `/subscription/get-card`,
      );
      if (status === 200) {
        setCardLoader(false);
        const cardDetails = data?.card;
        setCardList(cardDetails);
      }
    } catch (error) {
      setCardLoader(false);
    }
  };

  const handleCardSave = (event) => {
    setCardSave(event?.target?.checked);
  };

  const handlePaymentModeChange = (e, data) => {
    if (e?.target?.checked) {
      setSelectedCard(data);
      setPaymentMode(e?.target?.value);
      if (compId) {
        setCardFeeInfo({});
        validateUserCard({
          cardId: e?.target?.value,
          amount: planPrice ?? 0,
        });
      }
    } else {
      setSelectedCard("");
      setPaymentMode("");
      setCardFeeInfo({});
    }
  };

  const handlecardRemove = async (id) => {
    try {
      const { status, data } = await axiosInstance.delete(
        `/subscription/delete-card/${id}`,
        {
          body: {
            type: "web",
          },
        },
      );
      if (status === 200) {
        fetchGetCardDetails();
        setSelectedCard("");
      } else {
        setSelectedCard("");
      }
    } catch (error) {
      setCardLoader(false);
      setSelectedCard("");
    }
  };

  const fetchSubscriptionPaymentData = async () => {
    try {
      const { status, data } = await axiosInstance.get(`/user/get-user`);
      if (status === 200) {
        if (Config.release == "IN") {
          data["oddfixtures"] = false;
        }
        dispatch(fetchSubscriptionData(data));
      } else {
        dispatch(fetchSubscriptionData(undefined));
      }
    } catch (err) {
      dispatch(fetchSubscriptionData(undefined));
    }
  };

  // Billing Address API functions
  const fetchCountry = async (page) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/country?limit=20&offset=${page}`,
      );
      if (status === 200) {
        let response = data?.result?.rows;
        setCountryApiCount(data?.result?.count / 20);

        let newdata = [];
        let FinalData = response?.map((item) => {
          newdata.push({
            label: item?.country,
            value: item?.id,
          });
        });
        const finalCountryData = newdata.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        });
        let filterData = _.unionBy(country, finalCountryData);
        setCountry(
          _.uniqBy(filterData, function (e) {
            return e.value;
          }),
        );
      }
    } catch (err) {}
  };

  const handleOnScrollBottomCountry = (e) => {
    if (isCountrySearch === "" && countryApiCount !== pageCountry) {
      fetchCountry(pageCountry + 20);
      setpageCountry(pageCountry + 20);
    }
  };

  const handleCountryInputChange = (page, value) => {
    axiosInstance
      .get(`public/country?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          setsearchCountryCount(res?.data?.result?.count / 20);

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(searchCountry, newdata);
          setSearchCountry(
            _.uniqBy(filterData, function (e) {
              return e.value;
            }),
          );
          setIsCountrySearch(value);
        }
      });
  };

  const fetchState = async (id, page, type, statecount) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/state/country/${id}?limit=20&offset=${page}`,
      );
      if (status === 200) {
        let response = data?.result?.rows;
        setStateApiCount(data?.result?.count / 20);

        let newdata = [];
        let FinalData = response?.map((item) => {
          newdata.push({
            label: item?.state,
            value: item?.id,
          });
        });
        const finalStateData = newdata.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        });

        // If it's a new country selection (type === true), replace the state array completely
        if (type === true) {
          setState(finalStateData);
        } else {
          // For pagination, append to existing states
          let filterData = _.unionBy(state, finalStateData);
          setState(
            _.uniqBy(filterData, function (e) {
              return e.value;
            }),
          );
        }
      }
    } catch (err) {}
  };

  const handleOnScrollBottomState = (e) => {
    if (countryId && isStateSearch === "" && stateApiCount !== pageState) {
      fetchState(countryId, pageState + 20, false, stateApiCount);
      setpageState(pageState + 20);
    }
  };

  const handleStateInputChange = (page, value) => {
    if (!countryId) return;

    axiosInstance
      .get(
        `public/state/country/${countryId}?limit=20&offset=${page}&search=${value}`,
      )
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          setsearchStateCount(res?.data?.result?.count / 20);

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.state,
              value: item?.id,
            });
          });
          const finalStateData = newdata.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          });

          // For search, replace the search results completely for the new country
          if (page === 0) {
            setSearchState(finalStateData);
          } else {
            // For pagination in search, append to existing search results
            let filterData = _.unionBy(searchState, finalStateData);
            setSearchState(
              _.uniqBy(filterData, function (e) {
                return e.value;
              }),
            );
          }
          setIsStateSearch(value);
        }
      });
  };

  const handleSaveAddressChange = (event) => {
    setSaveAddressForFuture(event?.target?.checked);
  };

  const validationSchema = Yup.object().shape({
    cardname: paymentMode
      ? Yup.string()
      : Yup.string().required(localesData?.validation?.required_message),
    cardNumber: paymentMode
      ? Yup.string()
      : Yup.string()
          .min(19, "Card number must be at most 16 characters")
          .max(19, "Card number must be at most 16 characters")
          .required("Card number is required"),
    expirationDate: paymentMode
      ? Yup.string()
      : Yup.string()
          .required("Expiration date is required")
          .typeError("Not a valid expiration date. Example: MM/YYYY")
          .max(7, "Not a valid expiration date. Example: MM/YYYY")
          .matches(
            /([0-9]{2})\/([0-9]{4})/,
            "Not a valid expiration date. Example: MM/YYYY",
          )
          .test(
            "is-future",
            "Card expiration date should be in the future",
            function (value) {
              if (!value) return false;
              const currentDate = new Date();
              const [month, year] = value.split("/");
              const cardExpirationDate = new Date(
                parseInt(year),
                parseInt(month) - 1,
              );
              return cardExpirationDate > currentDate;
            },
          )
          .test(
            "not-over-100",
            "Card expiration date should not exceed 100 years in the future",
            function (value) {
              if (!value) return false;
              const currentDate = new Date();
              const [month, year] = value.split("/");
              const cardExpirationDate = new Date(
                parseInt(year),
                parseInt(month) - 1,
              );
              return (
                cardExpirationDate.getFullYear() <=
                currentDate.getFullYear() + 100
              );
            },
          ),
    cvv: paymentMode
      ? Yup.string()
      : Yup.string().min(3).max(3).required("CVV number is required"),
    // Billing Address validation
    billingStreetAddress: Yup.string()
      .nullable()
      .required(localesData?.validation?.required_message),
    billingAptSuite: Yup.string().nullable(),
    billingPostcode: Yup.string()
      .nullable()
      .required(localesData?.validation?.required_message),
    billingCitySuburb: Yup.string()
      .nullable()
      .required(localesData?.validation?.required_message),
    billingCountry: Yup.string()
      .nullable()
      .required(localesData?.validation?.required_message),
    billingState: Yup.string()
      .nullable()
      .required(localesData?.validation?.required_message),
  });


  return (
    <>
      <Box>
        <Box className="payment-select">
          <RadioGroup
            aria-label="payment"
            name="payment"
            className="payment-radioGroup"
            value={Number(paymentMode)}
          >
            {cardLoader && (
              <div className="allsport-loader-center">
                <Loader />
              </div>
            )}
            {!cardLoader &&
              (cardList?.length > 0 ? (
                cardList?.map((item) => {
                  return (
                    <>
                      <Box className="payment-card-flex">
                        <FormControlLabel
                          key={item.id}
                          className="save-card"
                          value={Number(item.id)}
                          onChange={(e) => handlePaymentModeChange(e, item)}
                          control={
                            <Checkbox
                              size="small"
                              icon={<UnChecked className="radio-icon" />}
                              checkedIcon={<Checked className="radio-icon" />}
                              disableRipple
                              checked={item?.id == paymentMode}
                            />
                          }
                          label={
                            <Box className="save-payment-card-label">
                              <Box>
                                <Typography className="bold">
                                  {item?.brand}
                                  <span>****{item?.last4}</span>
                                </Typography>
                                <Typography>{item?.cardHolderName}</Typography>
                                <Typography>
                                  Expiry - {item?.cardExp}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                        <Box>
                          <span
                            className={
                              item.is_default === 1
                                ? "disabled"
                                : "cursor-pointer"
                            }
                            onClick={() => {
                              if (item && item.is_default !== 1) {
                                handlecardRemove(item.id);
                              }
                            }}
                          >
                            Remove
                          </span>
                        </Box>
                      </Box>
                    </>
                  );
                })
              ) : (
                <></>
              ))}
            {/* <FormControlLabel
              value="Credit"
              control={
                <Radio
                  size="small"
                  icon={<UnChecked className="radio-icon" />}
                  checkedIcon={<Checked className="radio-icon" />}
                  disableRipple
                />
              }
              label={
                <Box className="payment-label">
                  <Typography className="label">
                    Credit Or Debit Card
                  </Typography>
                  <Box className="card-section">
                    <img src={MasterCard} alt="card" />
                    <img src={Visa} alt="card" />
                    <img src={AmericanExpress} alt="card" />
                  </Box>
                </Box>
              }
            /> */}
            {/* <FormControlLabel
              value="PayPal"
              control={
                <Radio
                  size="small"
                  icon={<UnChecked className="radio-icon" />}
                  checkedIcon={<Checked className="radio-icon" />}
                  disableRipple
                />
              }
              label=<Box className="payment-label">
                <Typography className="label">PayPal</Typography>
                <Box className="card-section">
                  <img src={PayPal} alt="card" />
                </Box>
              </Box>
            /> */}
          </RadioGroup>
        </Box>
        <Box className="card-details">
          <Formik
            initialValues={{
              cardname: selectedCard ? selectedCard?.cardHolderName : "",
              cardNumber: "",
              expirationDate: selectedCard
                ? moment(selectedCard?.cardExp).format("MM/YYYY")
                : "",
              cvv: "",
              // Billing Address fields
              billingStreetAddress: "",
              billingAptSuite: "",
              billingPostcode: "",
              billingCitySuburb: "",
              billingCountry: "",
              billingState: "",
            }}
            enableReinitialize={true}
            validationSchema={validationSchema}
            // onSubmit={async requestData => {
            //   console.log("requestData", requestData);

            // }}
            onSubmit={async (requestData, { resetForm }) => {
              setSubscribeLoading(true);
              const expirationDate = requestData?.expirationDate;
              const parts = expirationDate.split("/");
              let formattedDate = "";
              if (parts.length === 2) {
                const month = parts[0];
                const year = parts[1];
                formattedDate = `${year}-${month}`;
              }
              // let cardPayload = {
              //   card: {
              //     card_holder_name: requestData?.cardname,
              //     card_number: requestData?.cardNumber?.replace(/\s/g, ""),
              //     card_exp: formattedDate,
              //     cvv: requestData?.cvv,
              //   },
              // };
              if (compId) {
                let cardPayload = {
                  platform: "web",
                  cardId: Number(paymentMode),
                  billingAddress: {
                    streetAddress: requestData?.billingStreetAddress,
                    aptSuite: requestData?.billingAptSuite,
                    postcode: requestData?.billingPostcode,
                    citySuburb: requestData?.billingCitySuburb,
                    country: requestData?.billingCountry,
                    state: requestData?.billingState,
                    saveForFuture: saveAddressForFuture,
                  },
                };
                let payload = {
                  platform: "web",
                  isFeatured: cardsave,
                  card: {
                    card_holder_name: requestData?.cardname,
                    card_number: requestData?.cardNumber?.replace(/\s/g, ""),
                    card_exp: formattedDate,
                    cvv: requestData?.cvv,
                  },
                  billingAddress: {
                    streetAddress: requestData?.billingStreetAddress,
                    aptSuite: requestData?.billingAptSuite,
                    postcode: requestData?.billingPostcode,
                    citySuburb: requestData?.billingCitySuburb,
                    country: requestData?.billingCountry,
                    state: requestData?.billingState,
                    saveForFuture: saveAddressForFuture,
                  },
                };

                try {
                  let passApi = `/tipping/payment/${compId}`;
                  const { status, data } = await axiosInstance.post(
                    passApi,
                    paymentMode ? cardPayload : payload,
                  );
                  handlePaymentStatus(true);
                  if (status === 200) {
                    setSubscribeLoading(false);
                    setApiMessage("success", data?.message);
                    setCardSave(false);
                    setPaymentModalOpen(false);
                    setSelectedCard("");
                    let timer = setTimeout(() => resetForm(requestData), 3000);
                    handleSupportedTeamModal();
                  } else {
                    setSubscribeLoading(false);
                    setPaymentModalOpen(false);
                    setCardSave(false);
                    setSelectedCard("");
                  }
                } catch (error) {
                  handlePaymentStatus(false);
                  setSubscribeLoading(false);
                  setCardSave(false);
                  setSelectedCard("");
                  setApiMessage("error", error?.response?.data?.message);
                  setPaymentModalOpen(false);
                }
              } else {
                let cardPayload = {
                  plan_id: planId,
                  plateform: "web",
                  card_id: paymentMode,
                  couponCode: couponCode ? couponCode : "",
                  billingAddress: {
                    streetAddress: requestData?.billingStreetAddress,
                    aptSuite: requestData?.billingAptSuite,
                    postcode: requestData?.billingPostcode,
                    citySuburb: requestData?.billingCitySuburb,
                    country: requestData?.billingCountry,
                    state: requestData?.billingState,
                    saveForFuture: saveAddressForFuture,
                  },
                };
                let payload = {
                  plan_id: planId,
                  plateform: "web",
                  isFeatured: cardsave,
                  couponCode: couponCode ? couponCode : "",
                  card: {
                    card_holder_name: requestData?.cardname,
                    card_number: requestData?.cardNumber?.replace(/\s/g, ""),
                    card_exp: formattedDate,
                    cvv: requestData?.cvv,
                  },
                  billingAddress: {
                    streetAddress: requestData?.billingStreetAddress,
                    aptSuite: requestData?.billingAptSuite,
                    postcode: requestData?.billingPostcode,
                    citySuburb: requestData?.billingCitySuburb,
                    country: requestData?.billingCountry,
                    state: requestData?.billingState,
                    saveForFuture: saveAddressForFuture,
                  },
                };
                const isUpgrade = planDetails?.some(
                  (item) => item?.purchasedPlan === true,
                );
                try {
                  let passApi = isUpgrade
                    ? "/subscription/upgrade-plan"
                    : "/subscription/subscribe-plan";
                  let method = isUpgrade ? "put" : "post";
                  const { status, data } = await axiosInstance[method](
                    passApi,
                    paymentMode ? cardPayload : payload,
                  );
                  if (status === 200) {
                    // if (cardsave) {
                    //   setCardSaveLoading(true);
                    //   try {
                    //     const { status, data } = await axiosInstance.post(
                    //       "/subscription/create-card",
                    //       cardPayload
                    //     );
                    //     if (status === 200) {
                    //       setCardSaveLoading(false);
                    //       setCardSave(false);
                    //     } else {
                    //       setCardSaveLoading(false);
                    //       setCardSave(false);
                    //     }
                    //   } catch (error) {
                    //     setCardSaveLoading(false);
                    //     setCardSave(false);
                    //   }
                    // }

                    //call gatag script data

                    window.gtag(
                      "event",
                      "purchase",
                      googleTagData(data?.result?.transactionId),
                    );
                    setSubscribeLoading(false);
                    setApiMessage("success", data?.message);
                    setCardSave(false);
                    setPaymentModalOpen(false);
                    navigate("/mySubscription");
                    setSelectedCard("");
                    fetchGetPlanDetails();
                    fetchSubscriptionPaymentData();
                    let timer = setTimeout(
                      () => resetForm(requestData),
                      // setInterestedData([]),

                      3000,
                    );
                  } else {
                    setSubscribeLoading(false);
                    setPaymentModalOpen(false);
                    setCardSave(false);
                    setSelectedCard("");
                  }
                } catch (error) {
                  setSubscribeLoading(false);
                  setCardSave(false);
                  setSelectedCard("");
                  setApiMessage("error", error?.response?.data?.message);
                  setPaymentModalOpen(false);
                }
              }
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              touched,
              values,
              setFieldValue,
            }) => (
              <form onSubmit={handleSubmit}>
                <Box className="card-form">
                  <Box className="card-info mb-19">
                    <Typography className="textfield-text">
                      Name on Card<span className="star">*</span>
                    </Typography>
                    <TextField
                      id="outlined-basic"
                      variant="outlined"
                      className="details-textfield"
                      error={Boolean(touched.cardname && errors.cardname)}
                      helperText={touched.cardname && errors.cardname}
                      name="cardname"
                      value={values.cardname}
                      disabled={paymentMode}
                      onChange={(e) => {
                        handleChange(e);
                        // setCardUpdate({
                        //   ...cardUpdate,
                        //   card_holder_name: e.target.value
                        // });
                      }}
                      onBlur={(e) => {
                        handleBlur(e);
                        // updateCardDetails("cardname", values?.cardname);
                      }}
                    />
                  </Box>
                  <Box className="card-info mb-19">
                    <Typography className="textfield-text">
                      Card Number<span className="star">*</span>
                    </Typography>
                    <TextField
                      id="outlined-basic"
                      variant="outlined"
                      className="details-textfield"
                      name="cardNumber"
                      error={Boolean(touched?.cardNumber && errors?.cardNumber)}
                      helperText={touched?.cardNumber && errors?.cardNumber}
                      inputProps={{ maxLength: 19 }}
                      disabled={paymentMode}
                      onChange={(e) => {
                        handleChange(e);
                        // setCardUpdate({
                        //   ...cardUpdate,
                        //   card_token: e.target.value
                        // });
                        setFieldValue(
                          "cardNumber",
                          e?.target?.value
                            .replace(/[^\dA-Z*]/g, "")
                            .replace(/(.{4})/g, "$1 ")
                            .trim(),
                        );
                      }}
                      value={values?.cardNumber}
                      onKeyPress={(e) => {
                        if (!/[0-9 ]/.test(e.key)) {
                          e.preventDefault();
                        }
                      }}
                      onBlur={(e) => {
                        handleBlur(e);
                        // updateCardDetails(
                        //   "cardNumber",
                        //   values?.cardNumber
                        //     .replace(/[^\dA-Z*]/g, "")
                        //     .replace(/(.{4})/g, "$1 ")
                        //     .trim()
                        // );
                      }}
                    />
                  </Box>
                  <Box className="date-cvv-wrap mb-19">
                    <Box className="card-info date-card-info">
                      <Typography className="textfield-text">
                        Expiry Date<span className="star">*</span>
                      </Typography>
                      <TextField
                        id="outlined-basic"
                        variant="outlined"
                        className="details-textfield"
                        placeholder="mm/yyyy"
                        error={Boolean(
                          touched?.expirationDate && errors?.expirationDate,
                        )}
                        helperText={
                          touched?.expirationDate && errors?.expirationDate
                        }
                        name="expirationDate"
                        disabled={paymentMode}
                        value={values?.expirationDate}
                        onChange={(e) => {
                          setFieldValue(
                            "expirationDate",
                            e?.target?.value
                              .replace(/[^0-9]/g, "") // To allow only numbers
                              .replace(/^([2-9])$/g, "0$1") // To handle 3 > 03
                              .replace(/^(1{1})([3-9]{1})$/g, "0$1/$2") // 13 > 01/3
                              .replace(/^0{1,}/g, "0") // To handle 00 > 0
                              .replace(
                                /^([0-1]{1}[0-9]{1})([0-9]{1,4}).*/g,
                                "$1/$2",
                              ), // To handle 113 > 11/3
                          );
                          // setCardUpdate({
                          //   ...cardUpdate,
                          //   card_exp: e.target.value
                          // });
                        }}
                        inputProps={{ maxLength: 7 }}
                        onBlur={(e) => {
                          handleBlur(e);
                          // updateCardDetails(
                          //   "expirationDate",
                          //   values?.expirationDate
                          // );
                        }}
                      />
                    </Box>
                    <Box className="card-info cvv-card-info">
                      <Typography className="textfield-text cvv-text">
                        CVV <span className="star">*</span>
                        <img src={Cvvimage} alt="cvv" />
                      </Typography>
                      <TextField
                        id="outlined-basic"
                        variant="outlined"
                        className="details-textfield"
                        name="cvv"
                        type="password"
                        error={Boolean(touched?.cvv && errors?.cvv)}
                        disabled={paymentMode}
                        helperText={touched?.cvv && errors?.cvv}
                        value={values.cvv}
                        onChange={(e) => {
                          const re = /^[0-9\b]+$/;
                          if (
                            e.target.value === "" ||
                            re.test(e.target.value)
                          ) {
                            handleChange(e);
                          }
                          // setCardUpdate({
                          //   ...cardUpdate,
                          //   cvv: e.target.value
                          // });
                        }}
                        inputProps={{ maxLength: 3 }}
                        onBlur={(e) => {
                          handleBlur(e);
                          // updateCardDetails("cvv", values?.cvv);
                        }}
                      />
                    </Box>
                  </Box>

                  <Box className="checkBox-wrap">
                    <FormControlLabel
                      className="documentsRead-check"
                      control={
                        <Checkbox
                          className="documentsRead-checkbox"
                          checked={cardsave}
                          icon={<CheckboxUnChecked className="radio-icon" />}
                          checkedIcon={
                            <CheckBoxChecked className="radio-icon" />
                          }
                          onChange={(e) => handleCardSave(e)}
                          color="primary"
                        />
                      }
                      label={
                        <Typography>Save the card for future use</Typography>
                      }
                    />
                  </Box>
                  {/* Billing Address Section */}

                  <Typography
                    variant="h6"
                    style={{ marginBottom: "16px", fontWeight: "bold" }}
                  >
                    Billing Address
                  </Typography>

                  <Box className="date-cvv-wrap mb-19">
                    <Box className="card-info date-card-info">
                      <Typography className="textfield-text">
                        Street Address<span className="star">*</span>
                      </Typography>
                      <TextField
                        variant="outlined"
                        className="details-textfield"
                        name="billingStreetAddress"
                        error={Boolean(
                          touched?.billingStreetAddress &&
                            errors?.billingStreetAddress,
                        )}
                        helperText={
                          touched?.billingStreetAddress &&
                          errors?.billingStreetAddress
                        }
                        onChange={handleChange}
                        value={values?.billingStreetAddress}
                        onBlur={handleBlur}
                      />
                    </Box>
                    <Box className="card-info cvv-card-info">
                      <Typography className="textfield-text">
                        Apt/Suite (Optional)
                      </Typography>
                      <TextField
                        variant="outlined"
                        className="details-textfield"
                        name="billingAptSuite"
                        error={Boolean(
                          touched?.billingAptSuite && errors?.billingAptSuite,
                        )}
                        helperText={
                          touched?.billingAptSuite && errors?.billingAptSuite
                        }
                        onChange={handleChange}
                        value={values?.billingAptSuite}
                        onBlur={handleBlur}
                      />
                    </Box>
                  </Box>

                  <Box className="date-cvv-wrap mb-19">
                    <Box className="card-info date-card-info">
                      <Typography className="textfield-text">
                        Postcode<span className="star">*</span>
                      </Typography>
                      <TextField
                        variant="outlined"
                        className="details-textfield"
                        name="billingPostcode"
                        error={Boolean(
                          touched?.billingPostcode && errors?.billingPostcode,
                        )}
                        helperText={
                          touched?.billingPostcode && errors?.billingPostcode
                        }
                        onChange={handleChange}
                        value={values?.billingPostcode}
                        onBlur={handleBlur}
                      />
                    </Box>
                    <Box className="card-info cvv-card-info">
                      <Typography className="textfield-text">
                        City/Suburb<span className="star">*</span>
                      </Typography>
                      <TextField
                        variant="outlined"
                        className="details-textfield"
                        name="billingCitySuburb"
                        error={Boolean(
                          touched?.billingCitySuburb &&
                            errors?.billingCitySuburb,
                        )}
                        helperText={
                          touched?.billingCitySuburb &&
                          errors?.billingCitySuburb
                        }
                        onChange={handleChange}
                        value={values?.billingCitySuburb}
                        onBlur={handleBlur}
                      />
                    </Box>
                  </Box>

                  <Box className="date-cvv-wrap mb-19">
                    <Box className="card-info select-box-wrap">
                      <Typography className="textfield-text">
                        Country<span className="star">*</span>
                      </Typography>
                      <Select
                        className="select"
                        classNamePrefix="select"
                        isSearchable={true}
                        onMenuScrollToBottom={(e) =>
                          handleOnScrollBottomCountry(e)
                        }
                        onInputChange={(e) => handleCountryInputChange(0, e)}
                        placeholder="Country"
                        value={
                          isCountrySearch
                            ? searchCountry?.find((item) => {
                                return item?.value == country;
                              })
                            : country?.find((item) => {
                                const countryValue = values?.billingCountry;
                                return item?.value == countryValue;
                              })
                        }
                        onChange={(e) => {
                          setFieldValue("billingCountry", e?.value);
                          fetchState(e?.value, 0, true, 0);
                          setCountryId(e?.value);
                          setFieldValue("billingState", "");
                          setState([]);
                          setSearchState([]);
                          setIsStateSearch("");
                          setStateApiCount(0);
                          setpageState(0);
                          setisCountrySelectOpen(false);
                        }}
                        isFocused={isCountrySelectOpen}
                        onFocus={() => setisCountrySelectOpen(true)}
                        onBlur={() => setisCountrySelectOpen(false)}
                        options={isCountrySearch ? searchCountry : country}
                      />
                      {touched?.billingCountry && errors?.billingCountry && (
                        <Typography
                          variant="body2"
                          color="error"
                          style={{ fontSize: "12px", marginTop: "4px" }}
                        >
                          {errors?.billingCountry}
                        </Typography>
                      )}
                    </Box>

                    <Box className="card-info select-box-wrap">
                      <Typography className="textfield-text">
                        State<span className="star">*</span>
                      </Typography>
                      <Select
                        className="select"
                        classNamePrefix="select"
                        isSearchable={true}
                        isDisabled={!values?.billingCountry}
                        onMenuScrollToBottom={(e) =>
                          handleOnScrollBottomState(e)
                        }
                        onInputChange={(e) => handleStateInputChange(0, e)}
                        placeholder="State"
                        value={
                          !values?.billingCountry
                            ? null
                            : isStateSearch
                              ? searchState?.find((item) => {
                                  return item?.value == state;
                                })
                              : values?.billingState !== "" &&
                                state?.find((item) => {
                                  const stateValue = values?.billingState;
                                  return item?.value == stateValue;
                                })
                        }
                        onChange={(e) => {
                          setFieldValue("billingState", e?.value);
                          setisStateSelectOpen(false);
                          setStateApiCount(0);
                        }}
                        isFocused={isStateSelectOpen}
                        onFocus={() => setisStateSelectOpen(true)}
                        onBlur={() => setisStateSelectOpen(false)}
                        options={
                          !values?.billingCountry
                            ? []
                            : isStateSearch
                              ? searchState
                              : state
                        }
                      />
                      {touched?.billingState && errors?.billingState && (
                        <Typography
                          variant="body2"
                          color="error"
                          style={{ fontSize: "12px", marginTop: "4px" }}
                        >
                          {errors?.billingState}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  <Box
                    className="checkBox-wrap"
                    style={{ marginBottom: "16px" }}
                  >
                    <FormControlLabel
                      className="documentsRead-check"
                      control={
                        <Checkbox
                          className="documentsRead-checkbox"
                          checked={saveAddressForFuture}
                          icon={<CheckboxUnChecked className="radio-icon" />}
                          checkedIcon={
                            <CheckBoxChecked className="radio-icon" />
                          }
                          onChange={handleSaveAddressChange}
                          color="primary"
                        />
                      }
                      label={
                        <Typography>
                          Save this address to my profile for future payments
                        </Typography>
                      }
                    />
                  </Box>

                  {compId ? (
                    <>
                      <Box className="total-amount-card">
                        <Typography className="bold">Sub Total:</Typography>
                        <Typography>
                          {"$" + Number(planPrice)?.toFixed(2)}
                        </Typography>
                      </Box>{" "}
                      <Box className="total-amount-card border-bottom-card ">
                        <Typography className="bold">
                          Credit Card Fees:
                        </Typography>
                        <Typography>
                          {"$" +
                            (cardFeeInfo?.cardFee
                              ? Number(cardFeeInfo?.cardFee)?.toFixed(2)
                              : 0)}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    <></>
                  )}
                  <Box className="total-amount">
                    <Typography className="bold">Total Cost:</Typography>
                    <Typography>
                      {compId
                        ? "$" +
                          (cardFeeInfo?.totalAmount
                            ? Number(cardFeeInfo?.totalAmount)?.toFixed(2)
                            : 0)
                        : couponApply
                          ? "$" +
                            (
                              Number(planPrice ?? 0) -
                              Number(
                                couponCodeDetails?.discountAmountPrize ?? 0,
                              )
                            )?.toFixed(2)
                          : "$" + Number(planPrice)?.toFixed(2)}
                    </Typography>
                  </Box>
                  <Box className="subscribe-wrap-btn">
                    <Button
                      variant="contained"
                      type="submit"
                      className="subscribe-btn"
                      disabled={subscribeLoading}
                    >
                      {subscribeLoading
                        ? "Loading..."
                        : compId
                          ? "Pay to create a competition"
                          : "Subscribe"}
                    </Button>
                  </Box>
                  {!compId && (
                    <Box className="subscribe-note">
                      <Typography>
                        Plan renews automatically. You can change or cancel
                        anytime
                      </Typography>
                    </Box>
                  )}
                </Box>
                {(setCardFormValues(values), setCardFormError(errors))}
              </form>
            )}
          </Formik>
        </Box>
      </Box>
    </>
  );
};

export default PaymentModal;
