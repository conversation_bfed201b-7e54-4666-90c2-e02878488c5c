import React, { useState, useContext, useEffect } from "react";
import { Formik, Form, Field } from "formik";
import {
  Box,
  Checkbox,
  FormControlLabel,
  TextField,
  Typography,
} from "@mui/material";
import { ReactComponent as CheckBoxChecked } from "src/assets/images/checkBoxChecked.svg";
import { ReactComponent as CheckboxUnChecked } from "src/assets/images/checkboxUnChecked.svg";
import { Config, fetchFromStorage } from "src/helpers/context";
import axiosInstance from "src/helpers/Axios/axiosInstance";
import * as Yup from "yup";
import Select, { components } from "react-select";
import _ from "lodash";
import { IntlContext } from "src/App";
import { useSelector } from "react-redux";
import Loader from "src/components/Loader";
import DefaultImg from "src/assets/images/smartb_default.png";
import "./membershipFrom.scss";

const BillingAddress = ({ innerRef }) => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const { ValueContainer, Placeholder } = components;

  const reduxGetUserData = useSelector(
    (state) => state?.reduxData?.SubscripitionData,
  );

  const [getUserData, setGetUserData] = useState({});
  const [isDataFetch, setIsDataFetch] = useState(false);
  const [country, setCountry] = useState([]);
  const [state, setState] = useState([]);
  const [priorityCountry, setPriorityCountry] = useState([]);
  const [searchCountry, setSearchCountry] = useState([]);
  const [searchState, setSearchState] = useState([]);
  const [isCountrySearch, setIsCountrySearch] = useState(false);
  const [isStateSearch, setIsStateSearch] = useState(false);
  const [formvalues, setformvalues] = useState({});
  const [countryPage, setCountryPage] = useState(0);
  const [statePage, setStatePage] = useState(0);
  const [countryHasMore, setCountryHasMore] = useState(true);
  const [stateHasMore, setStateHasMore] = useState(true);

  useEffect(() => {
    if (reduxGetUserData && reduxGetUserData !== undefined) {
      setGetUserData(reduxGetUserData);
    }
  }, [reduxGetUserData]);

  useEffect(() => {
    fetchProrityCountry(0);
    setIsDataFetch(true);
  }, []);

  const handleRestrictedUser = () => {
    const localAuth = fetchFromStorage("auth_token");
    let isLogin = localAuth ? true : false;
    return isLogin;
  };

  const isLogin = handleRestrictedUser();

  // Fetch priority countries
  const fetchProrityCountry = async (page) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/country?limit=20&offset=${page}&countryId=13,230,231,101`,
      );
      if (status === 200) {
        let newdata = [];
        let track = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.country,
            value: item?.id,
            phoneCode: item?.phoneCode,
            flag: item?.country_flag?.includes("uploads")
              ? Config?.mediaURL + item?.country_flag
              : item?.country_flag,
          });
        });
        setPriorityCountry(newdata);
        fetchCountry(0, newdata);
      }
    } catch (err) {}
  };

  // Fetch countries
  const fetchCountry = async (page, priorityCountryData) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/country?limit=20&offset=${page}`,
      );
      if (status === 200) {
        let newdata = [];
        let track = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.country,
            value: item?.id,
            // phoneCode: item?.phoneCode,
            // flag: item?.country_flag?.includes("uploads")
            //   ? Config?.mediaURL + item?.country_flag
            //   : item?.country_flag,
          });
        });

        if (page === 0) {
          const combinedData = [...priorityCountryData, ...newdata];
          const uniqueData = _.uniqBy(combinedData, "value");
          setCountry(uniqueData);
        } else {
          setCountry((prev) => {
            const combinedData = [...prev, ...newdata];
            return _.uniqBy(combinedData, "value");
          });
        }

        if (data?.result?.rows?.length < 20) {
          setCountryHasMore(false);
        }
      }
    } catch (err) {}
  };

  // Fetch states
  const fetchState = async (page, countryId) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/state?limit=20&offset=${page}&countryId=${countryId}`,
      );
      if (status === 200) {
        let newdata = [];
        let track = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.state,
            value: item?.id,
          });
        });

        if (page === 0) {
          setState(newdata);
        } else {
          setState((prev) => [...prev, ...newdata]);
        }

        if (data?.result?.rows?.length < 20) {
          setStateHasMore(false);
        }
      }
    } catch (err) {}
  };

  // Handle country selection
  const handleCountryChange = (selectedOption, setFieldValue) => {
    setFieldValue("billingCountry", selectedOption?.value);
    setFieldValue("billingState", "");
    setState([]);
    setStatePage(0);
    setStateHasMore(true);
    if (selectedOption?.value) {
      fetchState(0, selectedOption?.value);
    }
    setformvalues((prevFormValues) => ({
      ...prevFormValues,
      billingCountry: selectedOption?.value,
      billingState: "",
    }));
  };

  // Handle state selection
  const handleStateChange = (selectedOption, setFieldValue) => {
    setFieldValue("billingState", selectedOption?.value);
    setformvalues((prevFormValues) => ({
      ...prevFormValues,
      billingState: selectedOption?.value,
    }));
  };

  // Handle country search
  const handleCountryInputChange = (page, inputValue) => {
    if (inputValue?.length > 0) {
      setIsCountrySearch(true);
      const filteredCountries = country?.filter((item) =>
        item?.label?.toLowerCase()?.includes(inputValue?.toLowerCase()),
      );
      setSearchCountry(filteredCountries);
    } else {
      setIsCountrySearch(false);
      setSearchCountry([]);
    }
  };

  // Handle state search
  const handleStateInputChange = (page, inputValue) => {
    if (inputValue?.length > 0) {
      setIsStateSearch(true);
      const filteredStates = state?.filter((item) =>
        item?.label?.toLowerCase()?.includes(inputValue?.toLowerCase()),
      );
      setSearchState(filteredStates);
    } else {
      setIsStateSearch(false);
      setSearchState([]);
    }
  };

  // Handle scroll for country dropdown
  const handleOnScrollBottomCountry = (e) => {
    if (countryHasMore && !isCountrySearch) {
      const newPage = countryPage + 1;
      setCountryPage(newPage);
      fetchCountry(newPage * 20, []);
    }
  };

  // Handle scroll for state dropdown
  const handleOnScrollBottomState = (e) => {
    if (stateHasMore && !isStateSearch && formvalues?.billingCountry) {
      const newPage = statePage + 1;
      setStatePage(newPage);
      fetchState(newPage * 20, formvalues?.billingCountry);
    }
  };

  // Custom option component for country dropdown
  const CustomOption = (props) => {
    const { data, innerRef, innerProps } = props;
    return (
      <div ref={innerRef} {...innerProps} className="custom-option">
        <img
          src={data?.flag || DefaultImg}
          alt={data?.label}
          className="flag-icon"
          onError={(e) => {
            e.target.src = DefaultImg;
          }}
        />
        <span>{data?.label}</span>
      </div>
    );
  };

  // Custom value container for country dropdown
  const CustomValueContainer = ({ children, ...props }) => {
    const { getValue, hasValue } = props;
    const selectedOption = hasValue ? getValue()[0] : null;

    return (
      <ValueContainer {...props}>
        {selectedOption && (
          <img
            src={selectedOption?.flag || DefaultImg}
            alt={selectedOption?.label}
            className="flag-icon-selected"
            onError={(e) => {
              e.target.src = DefaultImg;
            }}
          />
        )}
        {children}
      </ValueContainer>
    );
  };

  return (
    <Box className="your-details-wrap">
      <Typography className="details">Billing address</Typography>
      {!isDataFetch ? (
        <div className="allsport-loader-center ">
          <Loader />
        </div>
      ) : (
        <Formik
          enableReinitialize
          innerRef={innerRef}
          initialValues={{
            billingStreetAddress: "",
            billingAptSuite: "",
            billingPostcode: "",
            billingCitySuburb: "",
            billingCountry: "",
            billingState: "",
            saveAddressForFuture: false,
          }}
          validationSchema={Yup.object({
            billingStreetAddress: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingAptSuite: Yup.string().nullable(),
            billingPostcode: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingCitySuburb: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingCountry: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingState: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            saveAddressForFuture: Yup.boolean().nullable(),
          })}
          onSubmit={(values, { setSubmitting }) => {
            setSubmitting(false);
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            touched,
            values,
            setFieldValue,
          }) => (
            <form onSubmit={handleSubmit}>
              <Box className="personal-info-container">
                <Box className="personal-info grid-2">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingStreetAddress"
                    label="Street address"
                    error={Boolean(touched?.billingStreetAddress && errors?.billingStreetAddress)}
                    helperText={touched?.billingStreetAddress ? errors?.billingStreetAddress : ""}
                    onChange={handleChange}
                    value={values?.billingStreetAddress}
                    onBlur={handleBlur}
                  />
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingAptSuite"
                    label="Apt/Suite (Optional)"
                    error={Boolean(touched?.billingAptSuite && errors?.billingAptSuite)}
                    helperText={touched?.billingAptSuite ? errors?.billingAptSuite : ""}
                    onChange={handleChange}
                    value={values?.billingAptSuite}
                    onBlur={handleBlur}
                  />
                </Box>
                <Box className="personal-info grid-2">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingPostcode"
                    label="Postcode"
                    error={Boolean(touched?.billingPostcode && errors?.billingPostcode)}
                    helperText={touched?.billingPostcode ? errors?.billingPostcode : ""}
                    onChange={handleChange}
                    value={values?.billingPostcode}
                    onBlur={handleBlur}
                  />
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingCitySuburb"
                    label="City/Suburb"
                    error={Boolean(touched?.billingCitySuburb && errors?.billingCitySuburb)}
                    helperText={touched?.billingCitySuburb ? errors?.billingCitySuburb : ""}
                    onChange={handleChange}
                    value={values?.billingCitySuburb}
                    onBlur={handleBlur}
                  />
                </Box>
                <Box className="personal-info grid-2">
                  <Box className="select-box-wrap">
                    <Select
                      className="select"
                      classNamePrefix="select"
                      isSearchable={true}
                      onMenuScrollToBottom={(e) => handleOnScrollBottomCountry(e)}
                      placeholder={"Select"}
                      onInputChange={(e) => handleCountryInputChange(0, e)}
                      value={
                        isCountrySearch
                          ? searchCountry?.find((item) => {
                              return item?.value == formvalues?.billingCountry;
                            })
                          : country?.find((item) => {
                              const countryValue =
                                formvalues?.billingCountry &&
                                formvalues?.billingCountry !== ""
                                  ? formvalues?.billingCountry
                                  : values?.billingCountry;
                              return item?.value == countryValue;
                            })
                      }
                      options={isCountrySearch ? searchCountry : country}
                      onChange={(selectedOption) =>
                        handleCountryChange(selectedOption, setFieldValue)
                      }
                      components={{
                        Option: CustomOption,
                        ValueContainer: CustomValueContainer,
                      }}
                    />
                    <span className="text-danger">
                      {touched?.billingCountry && errors?.billingCountry
                        ? errors?.billingCountry
                        : ""}
                    </span>
                  </Box>
                  <Box className="select-box-wrap">
                    <Select
                      className="select"
                      classNamePrefix="select"
                      isSearchable={true}
                      onMenuScrollToBottom={(e) => handleOnScrollBottomState(e)}
                      placeholder={"Select"}
                      onInputChange={(e) => handleStateInputChange(0, e)}
                      value={
                        isStateSearch
                          ? searchState?.find((item) => {
                              return item?.value == formvalues?.billingState;
                            })
                          : state?.find((item) => {
                              const stateValue =
                                formvalues?.billingState &&
                                formvalues?.billingState !== ""
                                  ? formvalues?.billingState
                                  : values?.billingState;
                              return item?.value == stateValue;
                            })
                      }
                      options={isStateSearch ? searchState : state}
                      onChange={(selectedOption) =>
                        handleStateChange(selectedOption, setFieldValue)
                      }
                    />
                    <span className="text-danger">
                      {touched?.billingState && errors?.billingState
                        ? errors?.billingState
                        : ""}
                    </span>
                  </Box>
                </Box>
                <Box className="checkBox-wrap">
                  <FormControlLabel
                    className="documentsRead-check"
                    control={
                      <Checkbox
                        className="documentsRead-checkbox"
                        checked={values?.saveAddressForFuture}
                        icon={<CheckboxUnChecked className="radio-icon" />}
                        checkedIcon={<CheckBoxChecked className="radio-icon" />}
                        onChange={() =>
                          setFieldValue(
                            "saveAddressForFuture",
                            !values?.saveAddressForFuture,
                          )
                        }
                        color="primary"
                      />
                    }
                    label=""
                  />
                  <Typography className="policy-text">
                    Save this address to my profile for future payments
                  </Typography>
                </Box>
              </Box>
              {setformvalues(values)}
            </form>
          )}
        </Formik>
      )}
    </Box>
  );
};

export default BillingAddress;
