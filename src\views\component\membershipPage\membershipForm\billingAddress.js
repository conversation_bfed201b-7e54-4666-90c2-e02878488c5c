import React, { useState, useContext, useEffect } from "react";
import { Formik, Form, Field } from "formik";
import {
  Box,
  Checkbox,
  FormControlLabel,
  TextField,
  Typography,
} from "@mui/material";
import { ReactComponent as CheckBoxChecked } from "src/assets/images/checkBoxChecked.svg";
import { ReactComponent as CheckboxUnChecked } from "src/assets/images/checkboxUnChecked.svg";
import { fetchFromStorage } from "src/helpers/context";
import axiosInstance from "src/helpers/Axios/axiosInstance";
import * as Yup from "yup";
import Select, { components } from "react-select";
import _ from "lodash";
import { IntlContext } from "src/App";
import { useSelector } from "react-redux";
import Loader from "src/components/Loader";
import "./membershipFrom.scss";

const BillingAddress = ({ innerRef }) => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const { ValueContainer, Placeholder } = components;

  const reduxGetUserData = useSelector(
    (state) => state?.reduxData?.SubscripitionData,
  );

  const [getUserData, setGetUserData] = useState({});
  const [isDataFetch, setIsDataFetch] = useState(false);
  const [country, setCountry] = useState([]);
  const [state, setState] = useState([]);
  const [priorityCountry, setPriorityCountry] = useState([]);
  const [searchCountry, setSearchCountry] = useState([]);
  const [searchState, setSearchState] = useState([]);
  const [isCountrySearch, setIsCountrySearch] = useState("");
  const [isStateSearch, setIsStateSearch] = useState("");
  const [formvalues, setformvalues] = useState({});
  const [countryId, setCountryId] = useState("");
  const [countryApiCount, setCountryApiCount] = useState(0);
  const [stateApiCount, setStateApiCount] = useState(0);
  const [pageCountry, setpageCountry] = useState(0);
  const [pageState, setpageState] = useState(0);
  const [searchCountryCount, setsearchCountryCount] = useState(0);
  const [searchStateCount, setsearchStateCount] = useState(0);
  const [isCountrySelectOpen, setisCountrySelectOpen] = useState(false);
  const [isStateSelectOpen, setisStateSelectOpen] = useState(false);

  useEffect(() => {
    if (reduxGetUserData && reduxGetUserData !== undefined) {
      setGetUserData(reduxGetUserData);
    }
  }, [reduxGetUserData]);

  useEffect(() => {
    fetchCountry(0);
    setIsDataFetch(true);
  }, []);

  const CustomValueContainer = React.useMemo(() => {
    return ({ children, ...props }) => {
      return (
        <ValueContainer {...props}>
          <Placeholder {...props} isFocused={props.isFocused}>
            {props.selectProps.placeholder}
          </Placeholder>
          {React.Children.map(children, (child) => {
            if (child && child.type !== Placeholder) {
              return child;
            }
            return null;
          })}
        </ValueContainer>
      );
    };
  }, []);

  const handleRestrictedUser = () => {
    const localAuth = fetchFromStorage("auth_token");
    let isLogin = localAuth ? true : false;
    return isLogin;
  };

  const isLogin = handleRestrictedUser();

  // country select box api
  const fetchCountry = async (page) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/country?limit=20&offset=${page}`,
      );
      if (status === 200) {
        let response = data?.result?.rows;
        setCountryApiCount(data?.result?.count / 20);

        let newdata = [];
        let FinalData = response?.map((item) => {
          newdata.push({
            label: item?.country,
            value: item?.id,
          });
        });
        const finalCountryData = newdata.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        });
        let filterData = _.unionBy(country, finalCountryData);
        setCountry(
          _.uniqBy(filterData, function (e) {
            return e.value;
          }),
        );
      }
    } catch (err) {}
  };

  const handleOnScrollBottomCountry = (e) => {
    if (isCountrySearch === "" && countryApiCount !== pageCountry) {
      fetchCountry(pageCountry + 20);
      setpageCountry(pageCountry + 20);
    }
  };

  const handleCountryInputChange = (page, value) => {
    axiosInstance
      .get(`public/country?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          setsearchCountryCount(res?.data?.result?.count / 20);

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(searchCountry, newdata);
          setSearchCountry(
            _.uniqBy(filterData, function (e) {
              return e.value;
            }),
          );
          setIsCountrySearch(value);
        }
      });
  };

  const fetchState = async (id, page, type, statecount) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/state/country/${id}?limit=20&offset=${page}`,
      );
      if (status === 200) {
        let response = data?.result?.rows;
        setStateApiCount(data?.result?.count / 20);

        let newdata = [];
        let FinalData = response?.map((item) => {
          newdata.push({
            label: item?.state,
            value: item?.id,
          });
        });
        const finalStateData = newdata.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        });

        // If it's a new country selection (type === true), replace the state array completely
        if (type === true) {
          setState(finalStateData);
        } else {
          // For pagination, append to existing states
          let filterData = _.unionBy(state, finalStateData);
          setState(
            _.uniqBy(filterData, function (e) {
              return e.value;
            }),
          );
        }
      }
    } catch (err) {}
  };

  const handleOnScrollBottomState = (e) => {
    if (countryId && isStateSearch === "" && stateApiCount !== pageState) {
      fetchState(countryId, pageState + 20, false, stateApiCount);
      setpageState(pageState + 20);
    }
  };

  const handleStateInputChange = (page, value) => {
    if (!countryId) return;

    axiosInstance
      .get(
        `public/state/country/${countryId}?limit=20&offset=${page}&search=${value}`,
      )
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          setsearchStateCount(res?.data?.result?.count / 20);

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.state,
              value: item?.id,
            });
          });
          const finalStateData = newdata.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          });

          // For search, replace the search results completely for the new country
          if (page === 0) {
            setSearchState(finalStateData);
          } else {
            // For pagination in search, append to existing search results
            let filterData = _.unionBy(searchState, finalStateData);
            setSearchState(
              _.uniqBy(filterData, function (e) {
                return e.value;
              }),
            );
          }
          setIsStateSearch(value);
        }
      });
  };

  return (
    <Box className="your-details-wrap">
      <Typography className="details">Billing address</Typography>
      {!isDataFetch ? (
        <div className="allsport-loader-center ">
          <Loader />
        </div>
      ) : (
        <Formik
          enableReinitialize
          innerRef={innerRef}
          initialValues={{
            billingStreetAddress: "",
            billingAptSuite: "",
            billingPostcode: "",
            billingCitySuburb: "",
            billingCountry: "",
            billingState: "",
            saveAddressForFuture: false,
          }}
          validationSchema={Yup.object({
            billingStreetAddress: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingAptSuite: Yup.string().nullable(),
            billingPostcode: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingCitySuburb: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingCountry: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            billingState: Yup.string()
              .nullable()
              .required(localesData?.validation?.required_message),
            saveAddressForFuture: Yup.boolean().nullable(),
          })}
          onSubmit={(values, { setSubmitting }) => {
            setSubmitting(false);
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            touched,
            values,
            setFieldValue,
          }) => (
            <form onSubmit={handleSubmit}>
              <Box className="personal-info-container">
                <Box className="personal-info grid-2">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingStreetAddress"
                    label="Street address"
                    error={Boolean(
                      touched?.billingStreetAddress &&
                        errors?.billingStreetAddress,
                    )}
                    helperText={
                      touched?.billingStreetAddress
                        ? errors?.billingStreetAddress
                        : ""
                    }
                    onChange={handleChange}
                    value={values?.billingStreetAddress}
                    onBlur={handleBlur}
                  />
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingAptSuite"
                    label="Apt/Suite (Optional)"
                    error={Boolean(
                      touched?.billingAptSuite && errors?.billingAptSuite,
                    )}
                    helperText={
                      touched?.billingAptSuite ? errors?.billingAptSuite : ""
                    }
                    onChange={handleChange}
                    value={values?.billingAptSuite}
                    onBlur={handleBlur}
                  />
                </Box>
                <Box className="personal-info grid-2">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingPostcode"
                    label="Postcode"
                    error={Boolean(
                      touched?.billingPostcode && errors?.billingPostcode,
                    )}
                    helperText={
                      touched?.billingPostcode ? errors?.billingPostcode : ""
                    }
                    onChange={handleChange}
                    value={values?.billingPostcode}
                    onBlur={handleBlur}
                  />
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingCitySuburb"
                    label="City/Suburb"
                    error={Boolean(
                      touched?.billingCitySuburb && errors?.billingCitySuburb,
                    )}
                    helperText={
                      touched?.billingCitySuburb
                        ? errors?.billingCitySuburb
                        : ""
                    }
                    onChange={handleChange}
                    value={values?.billingCitySuburb}
                    onBlur={handleBlur}
                  />
                </Box>
                <Box className="personal-info grid-2">
                  <Box className="select-box-wrap ">
                    <Select
                      className="select"
                      classNamePrefix="select"
                      isSearchable={true}
                      onMenuScrollToBottom={(e) =>
                        handleOnScrollBottomCountry(e)
                      }
                      onInputChange={(e) => handleCountryInputChange(0, e)}
                      components={{
                        ValueContainer: CustomValueContainer,
                      }}
                      placeholder="Country"
                      styles={{
                        container: (provided, state) => ({
                          ...provided,
                        }),
                        valueContainer: (provided, state) => ({
                          ...provided,
                          overflow: "visible",
                        }),
                        placeholder: (provided, state) => ({
                          ...provided,
                          position: "absolute",
                          top:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? -11
                              : "auto",
                          backgroundColor:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "white"
                              : "transparent",
                          transition: "top 2s, font-size 0.1s !important",
                          fontSize:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "12px !important",
                          color:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "#4455c7"
                              : "#a4a4a4",
                          padding:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "0px 3px",
                          paddingLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "1px !important",
                          marginLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "7px !important",
                          lineHeight:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "8px !important",
                        }),
                      }}
                      value={
                        isCountrySearch
                          ? searchCountry?.find((item) => {
                              return item?.value == country;
                            })
                          : country?.find((item) => {
                              const countryValue = values?.billingCountry;
                              return item?.value == countryValue;
                            })
                      }
                      onChange={(e) => {
                        setFieldValue("billingCountry", e?.value);
                        setState([]);
                        setSearchState([]);
                        setIsStateSearch("");
                        fetchState(e?.value, 0, true, 0);
                        setCountryId(e?.value);
                        setFieldValue("billingState", "");
                        setStateApiCount(0);
                        setpageState(0);
                        setisCountrySelectOpen(false);
                        setformvalues((prevFormValues) => ({
                          ...prevFormValues,
                          billingCountry: e?.value,
                          billingState: "",
                        }));
                      }}
                      isFocused={isCountrySelectOpen}
                      onFocus={() => setisCountrySelectOpen(true)}
                      onBlur={() => setisCountrySelectOpen(false)}
                      options={isCountrySearch ? searchCountry : country}
                    />

                    <span className="text-danger">
                      {touched?.billingCountry && errors?.billingCountry
                        ? errors?.billingCountry
                        : ""}
                    </span>
                  </Box>
                  <Box className="select-box-wrap ">
                    <Select
                      className="select"
                      classNamePrefix="select"
                      isSearchable={true}
                      isDisabled={!values?.billingCountry}
                      onMenuScrollToBottom={(e) => handleOnScrollBottomState(e)}
                      onInputChange={(e) => handleStateInputChange(0, e)}
                      components={{
                        ValueContainer: CustomValueContainer,
                      }}
                      placeholder="State"
                      styles={{
                        container: (provided, state) => ({
                          ...provided,
                        }),
                        valueContainer: (provided, state) => ({
                          ...provided,
                          overflow: "visible",
                        }),
                        placeholder: (provided, state) => ({
                          ...provided,
                          position: "absolute",
                          top:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? -11
                              : "auto",
                          backgroundColor:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "white"
                              : "transparent",
                          transition: "top 2s, font-size 0.1s !important",
                          fontSize:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "12px !important",
                          color:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "#4455c7"
                              : "#a4a4a4",
                          padding:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "0px 3px",
                          paddingLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "1px !important",
                          marginLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "7px !important",
                          lineHeight:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "8px !important",
                        }),
                      }}
                      value={
                        !values?.billingCountry
                          ? null
                          : isStateSearch
                            ? searchState?.find((item) => {
                                return item?.value == state;
                              })
                            : values?.billingState !== "" &&
                              state?.find((item) => {
                                const stateValue = values?.billingState;
                                return item?.value == stateValue;
                              })
                      }
                      onChange={(e) => {
                        setFieldValue("billingState", e?.value);
                        setisStateSelectOpen(false);
                        setStateApiCount(0);
                        setformvalues((prevFormValues) => ({
                          ...prevFormValues,
                          billingState: e?.value,
                        }));
                      }}
                      isFocused={isStateSelectOpen}
                      onFocus={() => setisStateSelectOpen(true)}
                      onBlur={() => setisStateSelectOpen(false)}
                      options={
                        !values?.billingCountry
                          ? []
                          : isStateSearch
                            ? searchState
                            : state
                      }
                    />

                    <span className="text-danger">
                      {touched?.billingState && errors?.billingState
                        ? errors?.billingState
                        : ""}
                    </span>
                  </Box>
                </Box>
                <Box className="checkBox-wrap">
                  <FormControlLabel
                    className="documentsRead-check"
                    control={
                      <Checkbox
                        className="documentsRead-checkbox"
                        checked={values?.saveAddressForFuture}
                        icon={<CheckboxUnChecked className="radio-icon" />}
                        checkedIcon={<CheckBoxChecked className="radio-icon" />}
                        onChange={() =>
                          setFieldValue(
                            "saveAddressForFuture",
                            !values?.saveAddressForFuture,
                          )
                        }
                        color="primary"
                      />
                    }
                    label=""
                  />
                  <Typography className="policy-text">
                    Save this address to my profile for future payments
                  </Typography>
                </Box>
              </Box>
              {setformvalues(values)}
            </form>
          )}
        </Formik>
      )}
    </Box>
  );
};

export default BillingAddress;
